package com.inspeedia.vanning.domain;

import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * User entity representing users in the VMA system
 *
 * This entity stores basic user information for authentication purposes.
 */
@Table("users")
public class User extends BaseEntity {

    @NotBlank(message = "Username is required")
    @Size(max = 50, message = "Username must not exceed 50 characters")
    @Column("username")
    private String username;

    @NotBlank(message = "Password is required")
    @Size(max = 255, message = "Password must not exceed 255 characters")
    @Column("password")
    private String password;

    @Size(max = 100, message = "Full name must not exceed 100 characters")
    @Column("full_name")
    private String fullName;

    @Column("enabled")
    private Boolean enabled = true;

    public User() {
        super();
    }

    public User(String username, String password, String fullName, Boolean enabled) {
        super();
        this.username = username;
        this.password = password;
        this.fullName = fullName;
        this.enabled = enabled;
    }

    // Constructor with all fields including base entity fields
    public User(Long id, LocalDateTime createdAt, LocalDateTime updatedAt, Long version,
            String createdBy, String updatedBy, boolean deleted,
            String username, String password, String fullName, Boolean enabled) {
        super(id, createdAt, updatedAt, version, createdBy, updatedBy, deleted);
        this.username = username;
        this.password = password;
        this.fullName = fullName;
        this.enabled = enabled;
    }

    // Getters
    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public String getFullName() {
        return fullName;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    // Setters
    public void setUsername(String username) {
        this.username = username;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    // equals method (calls super)
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        User user = (User) o;
        return Objects.equals(username, user.username)
                && Objects.equals(password, user.password)
                && Objects.equals(fullName, user.fullName)
                && Objects.equals(enabled, user.enabled);
    }

    // hashCode method (calls super)
    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), username, password, fullName, enabled);
    }

    // toString method (calls super)
    @Override
    public String toString() {
        return "User{"
                + "username='" + username + '\''
                + ", fullName='" + fullName + '\''
                + ", enabled=" + enabled
                + ", " + super.toString()
                + '}';
    }
}
