package com.inspeedia.vanning.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for Excel import settings
 *
 * This allows easy modification of import parameters without code changes,
 * supporting various Excel formats and Japanese character encoding.
 */
@Configuration
@ConfigurationProperties(prefix = "excel.import")
public class ExcelImportConfig {

    /**
     * Row index where headers are located (0-based)
     */
    private int headerRowIndex = 1; // Row 2 in Excel (0-based index)

    /**
     * Row index where data starts (0-based)
     */
    private int dataStartRowIndex = 2; // Row 3 in Excel (0-based index)

    /**
     * Default values for missing fields
     */
    private DefaultValues defaultValues = new DefaultValues();

    /**
     * Validation settings
     */
    private ValidationSettings validation = new ValidationSettings();

    /**
     * Character encoding settings
     */
    private EncodingSettings encoding = new EncodingSettings();

    /**
     * Processing settings
     */
    private ProcessingSettings processing = new ProcessingSettings();

    /**
     * Audit settings
     */
    private AuditSettings audit = new AuditSettings();

    /**
     * Format settings for dates and times
     */
    private FormatSettings formats = new FormatSettings();

    /**
     * Error message templates
     */
    private ErrorMessages errors = new ErrorMessages();

    // Getters and Setters
    public int getHeaderRowIndex() {
        return headerRowIndex;
    }

    public void setHeaderRowIndex(int headerRowIndex) {
        this.headerRowIndex = headerRowIndex;
    }

    public int getDataStartRowIndex() {
        return dataStartRowIndex;
    }

    public void setDataStartRowIndex(int dataStartRowIndex) {
        this.dataStartRowIndex = dataStartRowIndex;
    }

    public DefaultValues getDefaultValues() {
        return defaultValues;
    }

    public void setDefaultValues(DefaultValues defaultValues) {
        this.defaultValues = defaultValues;
    }

    public ValidationSettings getValidation() {
        return validation;
    }

    public void setValidation(ValidationSettings validation) {
        this.validation = validation;
    }

    public EncodingSettings getEncoding() {
        return encoding;
    }

    public void setEncoding(EncodingSettings encoding) {
        this.encoding = encoding;
    }

    public ProcessingSettings getProcessing() {
        return processing;
    }

    public void setProcessing(ProcessingSettings processing) {
        this.processing = processing;
    }

    public AuditSettings getAudit() {
        return audit;
    }

    public void setAudit(AuditSettings audit) {
        this.audit = audit;
    }

    public FormatSettings getFormats() {
        return formats;
    }

    public void setFormats(FormatSettings formats) {
        this.formats = formats;
    }

    public ErrorMessages getErrors() {
        return errors;
    }

    public void setErrors(ErrorMessages errors) {
        this.errors = errors;
    }

    /**
     * Default values for missing fields
     */
    public static class DefaultValues {

        private String deliveryPlatform = "A";
        private String collectionPlatform = "B";
        private String size = "L1";

        public String getDeliveryPlatform() {
            return deliveryPlatform;
        }

        public void setDeliveryPlatform(String deliveryPlatform) {
            this.deliveryPlatform = deliveryPlatform;
        }

        public String getCollectionPlatform() {
            return collectionPlatform;
        }

        public void setCollectionPlatform(String collectionPlatform) {
            this.collectionPlatform = collectionPlatform;
        }

        public String getSize() {
            return size;
        }

        public void setSize(String size) {
            this.size = size;
        }
    }

    /**
     * Validation settings
     */
    public static class ValidationSettings {

        private String vanGpPattern = "^[A-Z0-9]{2}$";
        private int vanGpMinLength = 2;
        private int vanGpMaxLength = 2;
        private String[] invalidVanGpValues = {"0"};

        public String getVanGpPattern() {
            return vanGpPattern;
        }

        public void setVanGpPattern(String vanGpPattern) {
            this.vanGpPattern = vanGpPattern;
        }

        public int getVanGpMinLength() {
            return vanGpMinLength;
        }

        public void setVanGpMinLength(int vanGpMinLength) {
            this.vanGpMinLength = vanGpMinLength;
        }

        public int getVanGpMaxLength() {
            return vanGpMaxLength;
        }

        public void setVanGpMaxLength(int vanGpMaxLength) {
            this.vanGpMaxLength = vanGpMaxLength;
        }

        public String[] getInvalidVanGpValues() {
            return invalidVanGpValues;
        }

        public void setInvalidVanGpValues(String[] invalidVanGpValues) {
            this.invalidVanGpValues = invalidVanGpValues;
        }
    }

    /**
     * Character encoding settings for Japanese text
     */
    public static class EncodingSettings {

        private String defaultCharset = "UTF-8";
        private boolean supportJapanese = true;

        public String getDefaultCharset() {
            return defaultCharset;
        }

        public void setDefaultCharset(String defaultCharset) {
            this.defaultCharset = defaultCharset;
        }

        public boolean isSupportJapanese() {
            return supportJapanese;
        }

        public void setSupportJapanese(boolean supportJapanese) {
            this.supportJapanese = supportJapanese;
        }
    }

    /**
     * Processing settings for Excel import
     */
    public static class ProcessingSettings {

        private int skipHeaderRows = 2;
        private int batchSize = 100;
        private int concurrencyLevel = 16;

        public int getSkipHeaderRows() {
            return skipHeaderRows;
        }

        public void setSkipHeaderRows(int skipHeaderRows) {
            this.skipHeaderRows = skipHeaderRows;
        }

        public int getBatchSize() {
            return batchSize;
        }

        public void setBatchSize(int batchSize) {
            this.batchSize = batchSize;
        }

        public int getConcurrencyLevel() {
            return concurrencyLevel;
        }

        public void setConcurrencyLevel(int concurrencyLevel) {
            this.concurrencyLevel = concurrencyLevel;
        }
    }

    /**
     * Audit settings for tracking changes
     */
    public static class AuditSettings {

        private String createdBy = "excel-import";
        private String updatedBy = "excel-import";

        public String getCreatedBy() {
            return createdBy;
        }

        public void setCreatedBy(String createdBy) {
            this.createdBy = createdBy;
        }

        public String getUpdatedBy() {
            return updatedBy;
        }

        public void setUpdatedBy(String updatedBy) {
            this.updatedBy = updatedBy;
        }
    }

    /**
     * Format settings for date and time parsing
     */
    public static class FormatSettings {

        private String[] datePatterns = {"yyyy-MM-dd", "dd/MM/yyyy", "MM/dd/yyyy", "dd-MM-yyyy"};
        private String[] timePatterns = {"HH:mm:ss", "HH:mm", "H:mm", "H:mm:ss"};

        public String[] getDatePatterns() {
            return datePatterns;
        }

        public void setDatePatterns(String[] datePatterns) {
            this.datePatterns = datePatterns;
        }

        public String[] getTimePatterns() {
            return timePatterns;
        }

        public void setTimePatterns(String[] timePatterns) {
            this.timePatterns = timePatterns;
        }
    }

    /**
     * Error message templates
     */
    public static class ErrorMessages {

        private String vanGpRequired = "Van GP is required";
        private String vanGpPatternMismatch = "Van GP must match pattern: {0}";
        private String dateRequired = "{0} is required";
        private String dateInvalid = "Invalid {0} format: {1}";
        private String timeRequired = "{0} is required";
        private String timeInvalid = "Invalid {0} format: {1}";
        private String constraintViolation = "Constraint violation while saving record: {0}";
        private String transientDbError = "Transient database error (retryable): {0}";
        private String timeoutError = "Database operation timed out: {0}";
        private String generalDbError = "Database error: {0}";
        private String unknownDbError = "Unknown database error";

        public String getVanGpRequired() {
            return vanGpRequired;
        }

        public void setVanGpRequired(String vanGpRequired) {
            this.vanGpRequired = vanGpRequired;
        }

        public String getVanGpPatternMismatch() {
            return vanGpPatternMismatch;
        }

        public void setVanGpPatternMismatch(String vanGpPatternMismatch) {
            this.vanGpPatternMismatch = vanGpPatternMismatch;
        }

        public String getDateRequired() {
            return dateRequired;
        }

        public void setDateRequired(String dateRequired) {
            this.dateRequired = dateRequired;
        }

        public String getDateInvalid() {
            return dateInvalid;
        }

        public void setDateInvalid(String dateInvalid) {
            this.dateInvalid = dateInvalid;
        }

        public String getTimeRequired() {
            return timeRequired;
        }

        public void setTimeRequired(String timeRequired) {
            this.timeRequired = timeRequired;
        }

        public String getTimeInvalid() {
            return timeInvalid;
        }

        public void setTimeInvalid(String timeInvalid) {
            this.timeInvalid = timeInvalid;
        }

        public String getConstraintViolation() {
            return constraintViolation;
        }

        public void setConstraintViolation(String constraintViolation) {
            this.constraintViolation = constraintViolation;
        }

        public String getTransientDbError() {
            return transientDbError;
        }

        public void setTransientDbError(String transientDbError) {
            this.transientDbError = transientDbError;
        }

        public String getTimeoutError() {
            return timeoutError;
        }

        public void setTimeoutError(String timeoutError) {
            this.timeoutError = timeoutError;
        }

        public String getGeneralDbError() {
            return generalDbError;
        }

        public void setGeneralDbError(String generalDbError) {
            this.generalDbError = generalDbError;
        }

        public String getUnknownDbError() {
            return unknownDbError;
        }

        public void setUnknownDbError(String unknownDbError) {
            this.unknownDbError = unknownDbError;
        }
    }
}
