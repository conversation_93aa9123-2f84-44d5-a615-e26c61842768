-- VMA Database Migration
-- Adds missing columns to existing tables

-- Add missing columns to actual_work table if they don't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'van_gp')
    ALTER TABLE actual_work ADD van_gp NVARCHAR(2) CHECK (van_gp IS NULL OR (LEN(van_gp) = 2 AND van_gp = UPPER(van_gp)));

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'completed')
    ALTER TABLE actual_work ADD completed BIT DEFAULT 0;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'progress_rate')
    ALTER TABLE actual_work ADD progress_rate FLOAT CHECK (progress_rate >= 0.0 AND progress_rate <= 100.0);

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'version')
    ALTER TABLE actual_work ADD version BIGINT DEFAULT 0;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'deleted')
    ALTER TABLE actual_work ADD deleted BIT DEFAULT 0;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'created_by')
    ALTER TABLE actual_work ADD created_by NVARCHAR(50);

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'updated_by')
    ALTER TABLE actual_work ADD updated_by NVARCHAR(50);

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'vs')
    ALTER TABLE actual_work ADD vs TIME NULL;

-- Add missing columns to planned_work table if they don't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'version')
    ALTER TABLE planned_work ADD version BIGINT DEFAULT 0;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'deleted')
    ALTER TABLE planned_work ADD deleted BIT DEFAULT 0;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'created_by')
    ALTER TABLE planned_work ADD created_by NVARCHAR(50);

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'updated_by')
    ALTER TABLE planned_work ADD updated_by NVARCHAR(50);

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'shipping_date')
    ALTER TABLE planned_work ADD shipping_date NVARCHAR(10);

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'vs')
    ALTER TABLE planned_work ADD vs TIME NULL;

-- Add sheet_id and row_id columns for Excel order preservation
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'sheet_id')
    ALTER TABLE planned_work ADD sheet_id INT;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'row_id')
    ALTER TABLE planned_work ADD row_id INT;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'row_name')
    ALTER TABLE planned_work ADD row_name NVARCHAR(5);

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'test_id')
    ALTER TABLE planned_work DROP COLUMN test_id;

-- Update planned_work table to allow null values for van_gp, start_time, end_time, duration
-- Drop existing constraints if they exist
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CHK_Van_GP' AND parent_object_id = OBJECT_ID('planned_work'))
    ALTER TABLE planned_work DROP CONSTRAINT CHK_Van_GP;

-- Modify columns to allow nulls if they exist and are currently NOT NULL
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'van_gp' AND is_nullable = 0)
    ALTER TABLE planned_work ALTER COLUMN van_gp NVARCHAR(2) NULL;

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'start_time' AND is_nullable = 0)
    ALTER TABLE planned_work ALTER COLUMN start_time TIME NULL;

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'end_time' AND is_nullable = 0)
    ALTER TABLE planned_work ALTER COLUMN end_time TIME NULL;

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'duration' AND is_nullable = 0)
    ALTER TABLE planned_work ALTER COLUMN duration TIME NULL;

-- Create users table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
CREATE TABLE users (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    username NVARCHAR(50) NOT NULL UNIQUE,
    password NVARCHAR(255) NOT NULL,
    full_name NVARCHAR(100),
    enabled BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    created_by NVARCHAR(50),
    updated_by NVARCHAR(50),
    version BIGINT DEFAULT 0,
    deleted BIT DEFAULT 0
);

-- Create indexes for users table if they don't exist
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_users_username')
CREATE INDEX IX_users_username ON users(username) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_users_enabled')
CREATE INDEX IX_users_enabled ON users(enabled) WHERE deleted = 0;

-- Insert default admin user if no users exist
IF NOT EXISTS (SELECT * FROM users WHERE deleted = 0)
INSERT INTO users (username, password, full_name, enabled, created_by, updated_by)
VALUES ('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'Administrator', 1, 'system', 'system');