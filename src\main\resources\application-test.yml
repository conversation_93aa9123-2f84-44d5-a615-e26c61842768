spring:
  r2dbc:
    url: r2dbc:h2:mem:///testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: ""
    pool:
      initial-size: 1
      max-size: 5
      max-idle-time: 5m
      max-acquire-time: 10s
      max-create-connection-time: 10s
      validation-query: SELECT 1

  sql:
    init:
      mode: never

logging:
  level:
    root: WARN
    "[com.inspeedia.vanning]": DEBUG
    "[org.springframework.r2dbc]": DEBUG
    "[io.r2dbc]": DEBUG
