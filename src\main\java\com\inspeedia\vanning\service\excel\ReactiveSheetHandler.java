package com.inspeedia.vanning.service.excel;

import com.inspeedia.vanning.dto.RowDto;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.eventusermodel.XSSFSheetXMLHandler;
import org.apache.poi.xssf.usermodel.XSSFComment;
import reactor.core.publisher.FluxSink;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

class ReactiveSheetHandler implements XSSFSheetXMLHandler.SheetContentsHandler {

    private final String sheetName;
    private final int sheetId;
    private final FluxSink<RowDto> sink;
    private final List<String> currentRow = new ArrayList<>();
    private final AtomicInteger currentRowNum = new AtomicInteger(-1);
    private int lastColumnIndex = -1;

    ReactiveSheetHandler(String sheetName, int sheetId, FluxSink<RowDto> sink) {
        this.sheetName = sheetName;
        this.sheetId = sheetId;
        this.sink = sink;
    }

    @Override
    public void startRow(int rowNum) {
        currentRow.clear();
        currentRowNum.set(rowNum + 1);
        lastColumnIndex = -1;
    }

    @Override
    public void endRow(int rowNum) {
        sink.next(new RowDto(currentRowNum.get(), sheetName, sheetId, new ArrayList<>(currentRow)));
    }

    @Override
    public void cell(String cellReference, String formattedValue, XSSFComment comment) {
        // Parse the cell reference to get the column index
        CellReference ref = new CellReference(cellReference);
        int columnIndex = ref.getCol();

        // Fill any missing columns with empty strings
        while (currentRow.size() <= columnIndex) {
            currentRow.add("");
        }

        // Set the cell value at the correct index
        currentRow.set(columnIndex, formattedValue == null ? "" : formattedValue);
        lastColumnIndex = columnIndex;
    }

    @Override
    public void headerFooter(String text, boolean isHeader, String tagName) {
        // no-op
    }
}
