package com.inspeedia.vanning.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import org.springframework.web.reactive.config.WebFluxConfigurer;

/**
 * Web configuration for reactive Spring WebFlux
 *
 * This configuration class sets up CORS, content negotiation, and other
 * web-related configurations.
 */
@Configuration
public class WebConfig implements WebFluxConfigurer {

    private final Logger log = LoggerFactory.getLogger(WebConfig.class);

    private final AppProperties appProperties;

    public WebConfig(AppProperties appProperties) {
        this.appProperties = appProperties;
    }

    /**
     * Configures CORS for the application
     *
     * @return CorsWebFilter configured with application properties
     */
    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration corsConfig = new CorsConfiguration();
        corsConfig.setAllowedOrigins(appProperties.getCors().getAllowedOrigins());
        corsConfig.setAllowedMethods(appProperties.getCors().getAllowedMethods());

        // When allowCredentials is true, we need to specify allowed headers explicitly
        // instead of using "*" wildcard
        if (appProperties.getCors().isAllowCredentials()) {
            corsConfig.addAllowedHeader("Content-Type");
            corsConfig.addAllowedHeader("Authorization");
            corsConfig.addAllowedHeader("Accept");
            corsConfig.addAllowedHeader("Origin");
            corsConfig.addAllowedHeader("X-Requested-With");
            corsConfig.addAllowedHeader("Access-Control-Request-Method");
            corsConfig.addAllowedHeader("Access-Control-Request-Headers");
            corsConfig.addAllowedHeader("cache-control");
        } else {
            corsConfig.setAllowedHeaders(appProperties.getCors().getAllowedHeaders());
        }

        corsConfig.setAllowCredentials(appProperties.getCors().isAllowCredentials());
        corsConfig.setMaxAge(appProperties.getCors().getMaxAge());

        // Expose headers that the client can access
        corsConfig.addExposedHeader("Content-Disposition");
        corsConfig.addExposedHeader("Content-Type");

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig);

        log.info("CORS configured with origins: {}, methods: {}, allowCredentials: {}",
                appProperties.getCors().getAllowedOrigins(),
                appProperties.getCors().getAllowedMethods(),
                appProperties.getCors().isAllowCredentials());
        return new CorsWebFilter(source);
    }
}
