# Comment Standards for VMA API

This document defines the standardized comment format and guidelines for the VMA API project.

## General Principles

1. **Write code that is self-documenting** - Use meaningful names for variables, methods, and classes
2. **Comment the "why", not the "what"** - Explain business logic, not obvious code
3. **Keep comments concise and relevant** - Avoid over-commenting
4. **Update comments when code changes** - Stale comments are worse than no comments

## JavaDoc Standards

### Class-Level JavaDoc

```java
/**
 * Service for handling database migrations with resilient error handling
 * 
 * This service provides robust database migration capabilities that can handle
 * missing databases, column changes, and schema modifications without breaking
 * business logic. All operations include graceful error handling and detailed logging.
 */
@Service
public class DatabaseMigrationService {
```

**Format Rules:**
- First line: Brief description (one sentence)
- Empty line
- Detailed description (if needed)
- Use present tense
- No period after brief description
- Period after detailed description

### Method-Level JavaDoc

```java
/**
 * Creates a new actual work record with validation
 * 
 * @param actualWork the actual work data to create
 * @return Mono containing the created actual work with generated ID
 * @throws ValidationException if the actual work data is invalid
 */
public Mono<ActualWork> createActualWork(ActualWork actualWork) {
```

**Format Rules:**
- Brief description of what the method does
- `@param` for each parameter with description
- `@return` for return value description
- `@throws` for checked exceptions
- Use third person ("Creates", not "Create")

### Field-Level JavaDoc

```java
/**
 * Maximum number of retry attempts for database operations
 */
private static final int MAX_RETRY_ATTEMPTS = 3;

/**
 * Service for handling actual work operations
 */
private final ActualWorkService actualWorkService;
```

**When to Use:**
- Constants that aren't self-explanatory
- Complex configuration values
- Injected dependencies (optional, only if not obvious)

## Inline Comments

### When to Use Inline Comments

```java
// Handle legacy column names for backward compatibility
if (columnExists("user_name")) {
    renameColumn("user_name", "operator_name");
}

// Calculate progress rate as percentage (0-100)
double progressRate = (double) completedTasks / totalTasks * 100;

// Retry logic for transient database errors
return operation.retry(3)
    .onErrorResume(this::handleDatabaseError);
```

### When NOT to Use Inline Comments

```java
// BAD: Obvious comments
// Set the name
actualWork.setOperatorName(name);

// BAD: Redundant comments
// Get all actual work records
List<ActualWork> records = actualWorkService.findAll();

// BAD: Commented-out code (use version control instead)
// actualWork.setCompleted(true);
```

## Block Comments

Use block comments sparingly, mainly for:

```java
/*
 * Complex algorithm explanation:
 * 1. First, validate input parameters
 * 2. Then, calculate progress based on time spent
 * 3. Finally, update the database with new values
 * 
 * Note: This algorithm handles edge cases for weekend work
 */
```

## SQL Comments

```sql
-- Create actual_work table with audit fields
CREATE TABLE actual_work (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    -- Core work information
    work_date DATE NOT NULL,
    operator_name NVARCHAR(100) NOT NULL,
    -- Timing information
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    -- Audit fields for tracking changes
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);
```

## Configuration Comments

```yaml
# Database Configuration
spring:
  r2dbc:
    # Connection pool settings for optimal performance
    pool:
      initial-size: 5
      max-size: 20
      # Close idle connections after 30 minutes
      max-idle-time: 30m
```

## What NOT to Comment

### Avoid These Patterns

1. **Obvious Getters/Setters**
```java
// BAD
/**
 * Gets the ID
 * @return the ID
 */
public Long getId() {
    return id;
}
```

2. **Self-Explanatory Code**
```java
// BAD
// Check if actual work is completed
if (actualWork.isCompleted()) {
    // Process completed work
    processCompletedWork(actualWork);
}
```

3. **Redundant Information**
```java
// BAD
/**
 * ActualWork constructor that creates a new ActualWork
 */
public ActualWork() {
}
```

## Comment Maintenance

1. **Remove outdated comments** when refactoring
2. **Update comments** when changing business logic
3. **Remove TODO comments** once implemented
4. **Use issue tracking** instead of inline TODO comments for complex tasks

## Tools and Validation

- Use IDE formatting for consistent JavaDoc style
- Enable JavaDoc warnings in build process
- Review comments during code reviews
- Use static analysis tools to detect missing JavaDoc

## Examples of Good Comments

### Business Logic Comments
```java
/**
 * Calculates overtime hours based on Japanese labor law
 * 
 * Regular hours: up to 8 hours per day
 * Overtime: hours beyond 8 hours with 25% premium
 * Late night: hours between 22:00-05:00 with additional 25% premium
 */
private double calculateOvertimeHours(LocalTime startTime, LocalTime endTime) {
```

### Complex Algorithm Comments
```java
/**
 * Implements exponential backoff for database retry logic
 * 
 * Retry delays: 100ms, 200ms, 400ms, 800ms, 1600ms
 * Maximum retries: 5 attempts
 * Jitter: ±10% to prevent thundering herd
 */
private Mono<T> retryWithBackoff(Mono<T> operation) {
```

### Configuration Comments
```java
/**
 * Excel import configuration with Japanese locale support
 * 
 * Handles various Excel formats including legacy .xls files
 * and modern .xlsx files with proper character encoding for
 * Japanese text processing.
 */
@ConfigurationProperties(prefix = "excel.import")
public class ExcelImportConfig {
```

This standard ensures consistent, helpful comments throughout the VMA API codebase while avoiding over-commenting and maintaining code readability.
