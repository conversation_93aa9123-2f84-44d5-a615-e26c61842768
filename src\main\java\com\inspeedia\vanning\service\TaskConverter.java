package com.inspeedia.vanning.service;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.dto.TaskDto;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

@Service
public class TaskConverter {

    private static final Logger log = LoggerFactory.getLogger(TaskConverter.class);
    private final AlertService alertService;

    public TaskConverter(AlertService alertService) {
        this.alertService = alertService;
    }

    public TaskDto convertToTaskDto(PlannedWork plannedWork, ActualWork actualWork) {
        try {
            TaskDto task = new TaskDto();

            // Set basic properties
            task.setId(plannedWork.getId());
            task.setRowName(plannedWork.getRowName());
            task.setName(plannedWork.getOperatorName());
            task.setDate(plannedWork.getWorkDate().toString());
            task.setShippingDate(plannedWork.getShippingDate());
            task.setVanGp(plannedWork.getVanGp());
            task.setDeliveryTime(formatTime(plannedWork.getLoadTime()));
            task.setPlannedStart(formatTime(plannedWork.getStartTime()));
            task.setPlannedEnd(formatTime(plannedWork.getEndTime()));
            task.setPlannedDuration(formatDuration(plannedWork.getDuration()));
            task.setPlannedVs(formatTime(plannedWork.getVs()));

            // Set actual work data if available
            if (actualWork != null) {
                populateActualWorkData(task, plannedWork, actualWork);
            } else {
                populateEmptyActualWorkData(task);
            }

            return task;
        } catch (Exception e) {
            log.error("Error converting work {} to task DTO: {}", plannedWork.getId(), e.getMessage(), e);
            return createMinimalTask(plannedWork);
        }
    }

    private TaskDto createMinimalTask(PlannedWork plannedWork) {
        TaskDto task = new TaskDto();
        task.setId(plannedWork.getId());
        task.setName(plannedWork.getOperatorName());
        task.setDate(plannedWork.getWorkDate() != null ? plannedWork.getWorkDate().toString() : "");
        task.setShippingDate(plannedWork.getShippingDate() != null ? plannedWork.getShippingDate() : "");
        task.setVanGp(plannedWork.getVanGp());
        populateEmptyActualWorkData(task);
        return task;
    }

    private void populateEmptyActualWorkData(TaskDto task) {
        task.setActualStart("");
        task.setActualEnd("");
        task.setActualDuration("");
        task.setActualVs("");
        task.setProgress(0);
        task.setProgressRate(null);
        task.setAlerts(List.of());
    }

    private void populateActualWorkData(TaskDto task, PlannedWork plannedWork, ActualWork actualWork) {
        try {
            task.setActualStart(formatTime(actualWork.getStartTime()));
            task.setActualEnd(formatTime(actualWork.getEndTime()));
            task.setActualDuration(formatDuration(actualWork.getDuration()));
            task.setActualVs(formatDuration(actualWork.getVs()));

            Integer progress = actualWork.getProgress();
            Float progressRate = actualWork.getProgressRate();
            task.setProgress(progress != null ? progress : 0);
            task.setProgressRate(progressRate);

            try {
                task.setAlerts(alertService.calculateAlerts(plannedWork, actualWork));
            } catch (Exception e) {
                log.warn("Error calculating alerts for task {}: {}", plannedWork.getId(), e.getMessage());
                task.setAlerts(Collections.emptyList());
            }
        } catch (Exception e) {
            log.warn("Error populating actual work data for task {}: {}", plannedWork.getId(), e.getMessage());
            populateEmptyActualWorkData(task);
        }
    }

    /**
     * Format LocalTime to string (HH:mm format)
     */
    private String formatTime(LocalTime time) {
        if (time == null) {
            return "";
        }
        return time.format(DateTimeFormatter.ofPattern("HH:mm"));
    }

    /**
     * Format duration LocalTime to readable string
     */
    private String formatDuration(LocalTime duration) {
        if (duration == null) {
            return "";
        }
        int hours = duration.getHour();
        int minutes = duration.getMinute();

        if (minutes == 0) {
            return hours + "h";
        } else {
            return String.format("%dh %dm", hours, minutes);
        }
    }
}
