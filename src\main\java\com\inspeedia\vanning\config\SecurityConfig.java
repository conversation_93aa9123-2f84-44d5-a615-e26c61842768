package com.inspeedia.vanning.config;

import com.inspeedia.vanning.security.JwtAuthenticationFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.config.web.server.SecurityWebFiltersOrder;
import org.springframework.security.core.userdetails.MapReactiveUserDetailsService;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.web.server.context.NoOpServerSecurityContextRepository;

/**
 * Security configuration for the reactive application
 *
 * This configuration sets up basic authentication and authorization for the API
 * endpoints.
 */
@Configuration
@EnableWebFluxSecurity
public class SecurityConfig {

    private final Logger log = LoggerFactory.getLogger(SecurityConfig.class);
    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    public SecurityConfig(JwtAuthenticationFilter jwtAuthenticationFilter) {
        this.jwtAuthenticationFilter = jwtAuthenticationFilter;
    }

    /**
     * Configures the security filter chain
     */
    @Bean
    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {
        return http
                .csrf(ServerHttpSecurity.CsrfSpec::disable)
                .cors(cors -> {})
                .authorizeExchange(exchanges -> exchanges
                    // Allow all OPTIONS requests for CORS preflight
                    .pathMatchers(org.springframework.http.HttpMethod.OPTIONS, "/**").permitAll()
                    .pathMatchers(
                            "/actuator/**",
                            "/api-docs/**",
                            "/swagger-ui/**",
                            "/swagger-ui.html",
                            "/webjars/**",
                            "/api/v1/public/**",
                            "/api/v1/auth/**",
                            "/api/v1/tasks/**",
                            "/api/v1/tasks",
                            "/api/v1/planned-work/operator-names",
                            "/api/v1/planned-work/today",
                            "/api/v1/planned-work/upcoming",
                            "/api/v1/planned-work/**"
                    ).permitAll()
                        // Authenticated endpoints
                    .pathMatchers(
                            "/api/v1/planned-work/import",
                            "/api/v1/planned-work/template"
                    ).authenticated()
                        // Allow preflight CORS requests
                    .pathMatchers(HttpMethod.OPTIONS, "/**").permitAll()
                        // Default rule
                    .anyExchange().authenticated()
                )
                .addFilterBefore(jwtAuthenticationFilter, SecurityWebFiltersOrder.AUTHENTICATION)
                .httpBasic(httpBasic -> httpBasic
                    .securityContextRepository(NoOpServerSecurityContextRepository.getInstance())
                )
                .build();
    }

    /**
     * Password encoder bean
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * In-memory user details service for basic authentication In production,
     * this should be replaced with a proper user service
     */
    @Bean
    public MapReactiveUserDetailsService userDetailsService(PasswordEncoder passwordEncoder) {
        UserDetails admin = User.builder()
                .username("admin")
                .password(passwordEncoder.encode("admin123"))
                .roles("ADMIN")
                .build();

        UserDetails user = User.builder()
                .username("user")
                .password(passwordEncoder.encode("user123"))
                .roles("USER")
                .build();

        log.info("Configured in-memory users: admin, user");
        return new MapReactiveUserDetailsService(admin, user);
    }
}
