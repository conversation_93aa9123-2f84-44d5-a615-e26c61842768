
# Test Excel Import API
# $filePath = "src\main\resources\計画_20250919.xlsx"
$filePath = "C:\Dev\Mohan\Projects\MSS\Toyutsu\vanning\vma-api\src\main\resources\計画_20250919.xlsx"
$uri = "http://localhost:8081/api/v1/planned-work/import"

$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Check if file exists
$fileExists =Test-Path -LiteralPath $filePath
if ($fileExists) {
    Write-Host "Path exists: $filePath" -ForegroundColor Green
} else {
    Write-Host "Path does NOT exist: $filePath" -ForegroundColor Red
}
#     try {
#         Write-Host "Importing Excel file..."
#         # Read file as bytes
#         $fileBytes = [System.IO.File]::ReadAllBytes((Resolve-Path $filePath))
#         $fileName = [System.IO.Path]::GetFileName($filePath)

#         # Create boundary
#         $boundary = [System.Guid]::NewGuid().ToString()

#         # Create multipart form data
#         $LF = "`r`n"
#         $bodyLines = @(
#             "--$boundary",
#             "Content-Disposition: form-data; name=`"file`"; filename=`"$fileName`"",
#             "Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
#             "",
#             [System.Text.Encoding]::GetEncoding("iso-8859-1").GetString($fileBytes),
#             "--$boundary--"
#         ) -join $LF

#         $response = Invoke-RestMethod -Uri $uri -Method Post -Body $bodyLines -ContentType "multipart/form-data; boundary=$boundary"
#         Write-Host "Import successful!"
#         Write-Host "Response:"
#         $response | ConvertTo-Json -Depth 3
#     }
#     catch {
#         Write-Host "Import failed:"
#         Write-Host $_.Exception.Message
#         if ($_.Exception.Response) {
#             $stream = $_.Exception.Response.GetResponseStream()
#             $reader = New-Object System.IO.StreamReader($stream)
#             $responseBody = $reader.ReadToEnd()
#             Write-Host "Response body: $responseBody"
#         }
#     }
# } else {
#     Write-Host "File not found: $filePath"
# }