package com.inspeedia.vanning.service;

import java.io.ByteArrayInputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.inspeedia.vanning.config.ExcelColumnMapping;
import com.inspeedia.vanning.config.ExcelImportConfig;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.dto.ImportResultDto;
import com.inspeedia.vanning.dto.RowDto;
import com.inspeedia.vanning.repository.PlannedWorkRepository;
import com.inspeedia.vanning.service.excel.ExcelStreamingReader;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service for importing planned work data from Excel files
 * <p>
 * This service handles Excel file processing, validation, and database
 * operations for importing planned work records with proper error handling and
 * Japanese character encoding support.
 */
@Service
public class ExcelImportService {

    private final Logger log = LoggerFactory.getLogger(ExcelImportService.class);
    private final PlannedWorkRepository plannedWorkRepository;
    private final Validator validator;
    private final ExcelImportConfig config;
    private final ExcelStreamingReader excelStreamingReader;

    // State tracking for per-sheet header row skipping
    private final Map<String, Integer> sheetRowCounters = new ConcurrentHashMap<>();

    public ExcelImportService(PlannedWorkRepository plannedWorkRepository, Validator validator,
            ExcelImportConfig config, ExcelStreamingReader excelStreamingReader) {
        this.plannedWorkRepository = plannedWorkRepository;
        this.validator = validator;
        this.config = config;
        this.excelStreamingReader = excelStreamingReader;
    }

    /**
     * Imports planned work data from Excel file
     *
     * @param filePart the Excel file to import
     * @return Mono containing import result with success/error statistics
     */
    public Mono<ImportResultDto> importPlannedWorkFromExcel(FilePart filePart) {
        log.info("Starting Excel import for file: {}", filePart.filename());
        // Delegate to streaming implementation to keep public API stable
        return importPlannedWork(filePart)
                .doOnSuccess(result -> log.info("Excel import completed: {}", result))
                .doOnError(error -> log.error("Excel import failed: {}", error.getMessage()));
    }

    /**
     * Streams rows from Excel using Apache POI SAX-based API
     *
     * @param filePart the Excel file to stream from
     * @return Flux of RowDto containing Excel row data
     */
    public Flux<RowDto> importExcelStreaming(FilePart filePart) {
        return DataBufferUtils.join(filePart.content())
                .flatMapMany(dataBuffer -> {
                    byte[] bytes = new byte[dataBuffer.readableByteCount()];
                    dataBuffer.read(bytes);
                    DataBufferUtils.release(dataBuffer);

                    return Flux.using(
                            () -> new ByteArrayInputStream(bytes),
                            excelStreamingReader::read,
                            is -> {
                                try {
                                    is.close();
                                } catch (Exception ignore) {
                                }
                            }
                    );
                });
    }

    /**
     * Streaming import that maps rows to PlannedWork and saves with per-row
     * error capture.
     */
    public Mono<ImportResultDto> importPlannedWork(FilePart filePart) {
        LocalDate workDateFromFilename = extractDateFromFilename(filePart.filename());
        if (workDateFromFilename == null) {
            return Mono.just(new ImportResultDto(0, 0, 0, 1,
                    List.of("Invalid filename format. Expected format: <string>_yyMMdd.xlsx"),
                    List.of(), "Import failed: Invalid filename"));
        }
        if(workDateFromFilename.isAfter(LocalDate.now())) {
            return Mono.just(new ImportResultDto(0,0,0,1,
                    List.of("Invalid file date: " + workDateFromFilename +
                                    ". File dates cannot be in the future."),
                    List.of(), "Import failed: Future date not allowed"));
        }

        // Clear sheet row counters for fresh import
        sheetRowCounters.clear();

        return Mono.using(
                ImportState::new,
                state -> importExcelStreaming(filePart)
                        .filter(this::skipHeaderRowsPerSheet) // skip headers per sheet (configurable rows)
                        .filter(this::isEmptyRowStreamingNegated)
                        .map(row -> parseRowSafely(row, state, workDateFromFilename)) // Safe parsing with error tracking
                        .filter(ParseResult::isValid)
                        .map(ParseResult::getEntity)
                        .buffer(config.getProcessing().getBatchSize())
                        .concatMap(batch -> processBatch(batch, state), 1)
                        .then(createImportResult(state)),
                this::cleanupResources // Cleanup
        );
    }

    private boolean isEmptyRowStreamingNegated(RowDto row) {
        if (row == null || row.cells() == null) {
            return false;
        }
        for (String c : row.cells()) {
            if (c != null && !c.trim().isEmpty()) {
                return true;
            }
        }
        return false;
    }

    /**
     * Filters out header rows for each sheet individually.
     * This method tracks the row count per sheet and skips the configured number
     * of header rows at the beginning of each sheet.
     *
     * @param row the row to evaluate
     * @return true if the row should be processed (not a header row), false otherwise
     */
    private boolean skipHeaderRowsPerSheet(RowDto row) {
        if (row == null || row.sheetName() == null) {
            return false;
        }

        String sheetKey = row.sheetName();
        int currentRowCount = sheetRowCounters.compute(sheetKey, (key, count) ->
            count == null ? 1 : count + 1);

        // Skip if we haven't reached the data rows yet for this sheet
        int skipRows = config.getProcessing().getSkipHeaderRows();
        boolean shouldSkip = currentRowCount <= skipRows;

        if (shouldSkip) {
            log.debug("Skipping header row {} in sheet '{}' (skip first {} rows)",
                currentRowCount, sheetKey, skipRows);
        }

        return !shouldSkip;
    }

    private static class ImportState {
        private final List<String> errors = Collections.synchronizedList(new ArrayList<>());
        private final AtomicInteger total = new AtomicInteger(0);
        private final AtomicInteger saved = new AtomicInteger(0);
        
        public void addError(String error) {
            errors.add(error);
        }
        
        public void incrementTotal() {
            total.incrementAndGet();
        }
        
        public void incrementSaved() {
            saved.incrementAndGet();
        }
        
        public ImportResultDto toResultDto() {
            int failed = errors.size();
            return new ImportResultDto(
                total.get(), 
                saved.get(), 
                0, 
                failed, 
                new ArrayList<>(errors), 
                List.of(),
                String.format("Import completed: %d saved, %d failed", saved.get(), failed)
            );
        }
    }

    private static class ParseResult {
        private final boolean valid;
        private final RowEntity entity;
        private final String errorMessage;
        
        public ParseResult(RowEntity entity) {
            this.valid = true;
            this.entity = entity;
            this.errorMessage = null;
        }
        
        public ParseResult(String errorMessage) {
            this.valid = false;
            this.entity = null;
            this.errorMessage = errorMessage;
        }
        
        public boolean isValid() { return valid; }
        public RowEntity getEntity() { return entity; }
        public String getErrorMessage() { return errorMessage; }
    }

    private LocalDate extractDateFromFilename(String filename) {
        try {
            // Remove file extension and extract date part
            String baseName = filename.replaceAll("\\.xlsx?$", "");

            // Pattern: anything_yyyyMMdd
            Pattern pattern = Pattern.compile(".*_(\\d{8})$");
            Matcher matcher = pattern.matcher(baseName);

            if (matcher.find()) {
                String dateStr = matcher.group(1);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                return LocalDate.parse(dateStr, formatter);
            }

            // // Alternative pattern: check if filename contains 6 digits anywhere
            // Pattern altPattern = Pattern.compile(".*(\\d{8}).*");
            // Matcher altMatcher = altPattern.matcher(baseName);

            // if (altMatcher.find()) {
            //     String dateStr = altMatcher.group(1);
            //     DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            //     return LocalDate.parse(dateStr, formatter);
            // }

            return null;
        } catch (Exception e) {
            log.warn("Failed to extract date from filename: {}", filename, e);
            return null;
        }
    }

    // Safe row parsing with error handling
    private ParseResult parseRowSafely(RowDto row, ImportState state, LocalDate workDateFromFilename) {
        try {
            RowMappingResult result = toPlannedWork(row);
            if (!result.isValid) {
                String error = String.format("Sheet '%s' Row %d: %s", 
                    result.sheetName, result.rowNum, result.errorMessage);
                state.addError(error);
                return new ParseResult(error);
            }
            // Override the workDate with the one from filename
            result.entity.setWorkDate(workDateFromFilename);
            state.incrementTotal();
            return new ParseResult(new RowEntity(result.rowNum, result.sheetName, result.entity));
        } catch (Exception e) {
            String error = String.format("Row parsing failed: %s", e.getMessage());
            state.addError(error);
            return new ParseResult(error);
        }
    }

    // Batch processing with transaction management
    @Transactional(propagation = Propagation.REQUIRED)
    private Flux<PlannedWork> processBatch(List<RowEntity> batch, ImportState state) {
        return Flux.fromIterable(batch)
            .flatMap(re -> processSingleEntity(re, state), 
                config.getProcessing().getConcurrencyLevel(), 1) // Controlled concurrency + backpressure
            .onErrorContinue((error, obj) -> {
                log.warn("Batch processing skipped due to error: {}", error.getMessage());
                state.addError("Batch processing failed: " + error.getMessage());
            });
    }

    // Single entity processing with proper error handling
    private Mono<PlannedWork> processSingleEntity(RowEntity re, ImportState state) {
        return plannedWorkRepository
                .findFirstByWorkDateAndRowNameAndOperatorNameAndDeletedFalse(
                        re.entity.getWorkDate(),
                        re.entity.getRowName(),
                        re.entity.getOperatorName()
                )
                .flatMap(existing -> updateExistingEntity(existing, re))
                .switchIfEmpty(Mono.defer(() -> saveNewEntity(re)))
                .doOnSuccess(pw -> state.incrementSaved())
                .onErrorResume(e -> handleEntityError(e, re, state));
    }

    private Mono<PlannedWork> updateExistingEntity(PlannedWork existing, RowEntity re) {
        existing.setLoadTime(re.entity.getLoadTime());
        existing.setSize(re.entity.getSize());
        existing.setStartTime(re.entity.getStartTime());
        existing.setEndTime(re.entity.getEndTime());
        existing.setDuration(re.entity.getDuration());
        existing.setUpdatedAt(LocalDateTime.now());
        existing.setUpdatedBy(config.getAudit().getUpdatedBy());

        return plannedWorkRepository.save(existing);
    }

    private Mono<PlannedWork> saveNewEntity(RowEntity re) {
        re.entity.setCreatedAt(LocalDateTime.now());
        re.entity.setCreatedBy(config.getAudit().getCreatedBy());
        return plannedWorkRepository.save(re.entity);
    }

    /**
     * Parse Excel row to PlannedWork entity
     */
    private RowMappingResult toPlannedWork(RowDto row) {
        try {
            // Map using ExcelColumnMapping indices assuming same positions as workbook mode
            PlannedWork plannedWork = new PlannedWork();

            String dateStr = safeCell(row, ExcelColumnMapping.DATE.getColumnIndex());
            plannedWork.setWorkDate(parseDate(dateStr, "Date"));

            String shippingDate = safeCell(row, ExcelColumnMapping.DATE_TYPE.getColumnIndex());
            plannedWork.setShippingDate(shippingDate);

            String vanGp = safeCell(row, ExcelColumnMapping.VAN_GP.getColumnIndex()).trim().toUpperCase();
            if (!vanGp.isEmpty()) {
                if (isInvalidVanGp(vanGp)) {
                    throw new IllegalArgumentException(config.getErrors().getVanGpRequired());
                }
                if (!vanGp.matches(config.getValidation().getVanGpPattern())) {
                    throw new IllegalArgumentException(formatMessage(config.getErrors().getVanGpPatternMismatch(), config.getValidation().getVanGpPattern()));
                }
                plannedWork.setVanGp(vanGp);
            } else {
                plannedWork.setVanGp(null);
            }

            String rowName = safeCell(row, ExcelColumnMapping.ROW_NAME.getColumnIndex());
            plannedWork.setRowName(rowName);
            plannedWork.setOperatorName(row.sheetName());
            plannedWork.setDeliveryPlatform(config.getDefaultValues().getDeliveryPlatform());
            plannedWork.setCollectionPlatform(config.getDefaultValues().getCollectionPlatform());
            plannedWork.setSize(config.getDefaultValues().getSize());

            String loadTimeStr = safeCell(row, ExcelColumnMapping.LOAD_TIME.getColumnIndex());
            plannedWork.setLoadTime(parseTime(loadTimeStr, "Load Time"));

            String startTimeStr = safeCell(row, ExcelColumnMapping.START_TIME.getColumnIndex());
            plannedWork.setStartTime(parseTimeOptional(startTimeStr, "Start Time"));

            String endTimeStr = safeCell(row, ExcelColumnMapping.END_TIME.getColumnIndex());
            plannedWork.setEndTime(parseTimeOptional(endTimeStr, "End Time"));

            String durationStr = safeCell(row, ExcelColumnMapping.WORK_TIME.getColumnIndex());
            plannedWork.setDuration(parseTimeOptional(durationStr, "Duration"));

            LocalDateTime now = LocalDateTime.now();
            plannedWork.setCreatedAt(now);
            plannedWork.setUpdatedAt(now);
            plannedWork.setCreatedBy(config.getAudit().getCreatedBy());
            plannedWork.setUpdatedBy(config.getAudit().getUpdatedBy());

            // Set sheet and row information for order preservation
            plannedWork.setSheetId(row.sheetId());
            plannedWork.setRowId(row.rowNum());

            Set<ConstraintViolation<PlannedWork>> violations = validator.validate(plannedWork);
            if (!violations.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                for (ConstraintViolation<PlannedWork> v : violations) {
                    sb.append(v.getMessage()).append("; ");
                }
                return RowMappingResult.invalid(row.rowNum(), row.sheetName(), sb.toString());
            }

            return RowMappingResult.valid(row.rowNum(), row.sheetName(), plannedWork);
        } catch (Exception e) {
            return RowMappingResult.invalid(row.rowNum(), row.sheetName(), e.getMessage());
        }
    }

    private String safeCell(RowDto row, int index) {
        List<String> cells = row.cells();
        if (index < 0 || index >= cells.size()) {
            return "";
        }
        return cells.get(index) == null ? "" : cells.get(index);
    }

    private record RowEntity(int rowNum, String sheetName, PlannedWork entity) {

    }

    private static class RowMappingResult {

        final int rowNum;
        final String sheetName;
        final boolean isValid;
        final PlannedWork entity;
        final String errorMessage;

        private RowMappingResult(int rowNum, String sheetName, boolean isValid, PlannedWork entity, String errorMessage) {
            this.rowNum = rowNum;
            this.sheetName = sheetName;
            this.isValid = isValid;
            this.entity = entity;
            this.errorMessage = errorMessage;
        }

        static RowMappingResult valid(int rowNum, String sheetName, PlannedWork entity) {
            return new RowMappingResult(rowNum, sheetName, true, entity, null);
        }

        static RowMappingResult invalid(int rowNum, String sheetName, String errorMessage) {
            return new RowMappingResult(rowNum, sheetName, false, null, errorMessage);
        }
    }

    /**
     * Parse date string to LocalDate
     */
    private LocalDate parseDate(String dateStr, String fieldName) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            throw new IllegalArgumentException(formatMessage(config.getErrors().getDateRequired(), fieldName));
        }

        try {
            // Try configured date formats
            String[] patterns = config.getFormats().getDatePatterns();
            DateTimeFormatter[] formatters = new DateTimeFormatter[patterns.length];
            for (int i = 0; i < patterns.length; i++) {
                formatters[i] = DateTimeFormatter.ofPattern(patterns[i]);
            }

            for (DateTimeFormatter formatter : formatters) {
                try {
                    return LocalDate.parse(dateStr.trim(), formatter);
                } catch (DateTimeParseException ignored) {
                    // Try next format
                }
            }

            throw new IllegalArgumentException(formatMessage(config.getErrors().getDateInvalid(), fieldName, dateStr));
        } catch (Exception e) {
            throw new IllegalArgumentException(formatMessage(config.getErrors().getDateInvalid(), fieldName, dateStr));
        }
    }

    /**
     * Parse time string to LocalTime
     */
    private LocalTime parseTime(String timeStr, String fieldName) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            throw new IllegalArgumentException(formatMessage(config.getErrors().getTimeRequired(), fieldName));
        }

        try {
            // Try configured time formats
            String[] patterns = config.getFormats().getTimePatterns();
            DateTimeFormatter[] formatters = new DateTimeFormatter[patterns.length];
            for (int i = 0; i < patterns.length; i++) {
                formatters[i] = DateTimeFormatter.ofPattern(patterns[i]);
            }

            for (DateTimeFormatter formatter : formatters) {
                try {
                    return LocalTime.parse(timeStr.trim(), formatter);
                } catch (DateTimeParseException ignored) {
                    // Try next format
                }
            }

            throw new IllegalArgumentException(formatMessage(config.getErrors().getTimeInvalid(), fieldName, timeStr));
        } catch (Exception e) {
            throw new IllegalArgumentException(formatMessage(config.getErrors().getTimeInvalid(), fieldName, timeStr));
        }
    }

    /**
     * Parse time string to LocalTime, allowing null/empty values
     */
    private LocalTime parseTimeOptional(String timeStr, String fieldName) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }

        try {
            // Try configured time formats
            String[] patterns = config.getFormats().getTimePatterns();
            DateTimeFormatter[] formatters = new DateTimeFormatter[patterns.length];
            for (int i = 0; i < patterns.length; i++) {
                formatters[i] = DateTimeFormatter.ofPattern(patterns[i]);
            }

            for (DateTimeFormatter formatter : formatters) {
                try {
                    return LocalTime.parse(timeStr.trim(), formatter);
                } catch (DateTimeParseException ignored) {
                    // Try next format
                }
            }

            throw new IllegalArgumentException(formatMessage(config.getErrors().getTimeInvalid(), fieldName, timeStr));
        } catch (Exception e) {
            throw new IllegalArgumentException(formatMessage(config.getErrors().getTimeInvalid(), fieldName, timeStr));
        }
    }

    private String mapDbError(Throwable e) {
        String msg = e.getMessage() == null ? config.getErrors().getUnknownDbError() : e.getMessage();
        // Provide concise, user-friendly messages; could inspect vendor codes if needed
        if (e instanceof org.springframework.dao.DataIntegrityViolationException) {
            return formatMessage(config.getErrors().getConstraintViolation(), msg);
        }
        if (e instanceof io.r2dbc.spi.R2dbcTransientResourceException) {
            return formatMessage(config.getErrors().getTransientDbError(), msg);
        }
        if (e instanceof io.r2dbc.spi.R2dbcTimeoutException) {
            return formatMessage(config.getErrors().getTimeoutError(), msg);
        }
        return formatMessage(config.getErrors().getGeneralDbError(), msg);
    }

    /**
     * Check if a Van GP value is in the list of invalid values
     */
    private boolean isInvalidVanGp(String vanGp) {
        String[] invalidValues = config.getValidation().getInvalidVanGpValues();
        for (String invalidValue : invalidValues) {
            if (invalidValue.equals(vanGp)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Format message with single parameter replacement
     */
    private String formatMessage(String template, String param) {
        return template.replace("{0}", param);
    }

    /**
     * Format message with two parameter replacements
     */
    private String formatMessage(String template, String param1, String param2) {
        return template.replace("{0}", param1).replace("{1}", param2);
    }

    // Error handling for single entity
    private Mono<PlannedWork> handleEntityError(Throwable e, RowEntity re, ImportState state) {
        String errorMsg = String.format("Sheet '%s' Row %d: %s", 
            re.sheetName, re.rowNum, mapDbError(e));
        state.addError(errorMsg);
        log.warn("Entity processing failed: {}", errorMsg);
        return Mono.empty();
    }

    // Result creation
    private Mono<ImportResultDto> createImportResult(ImportState state) {
        return Mono.fromCallable(state::toResultDto)
            .doOnSuccess(result -> log.info("Import completed: {}", result.getMessage()))
            .doOnError(e -> log.error("Result creation failed: {}", e.getMessage()));
    }

    // Global error handling
    private Mono<ImportResultDto> handleImportError(Throwable e, FilePart filePart) {
        log.error("Import failed for file: {}", filePart.filename(), e);
        
        ImportResultDto errorResult = new ImportResultDto(
            0, 0, 0, 1, 
            List.of("Import failed: " + e.getMessage()), 
            List.of(),
            "Import failed due to unexpected error"
        );
        
        return Mono.just(errorResult);
    }

    // Resource cleanup
    private void cleanupResources(ImportState state) {
        log.debug("Cleaning up import resources");
        // Add any necessary cleanup logic
    }
}
