package com.inspeedia.vanning.dto;

/**
 * DTO for login response
 */
public class LoginResponse {

    private boolean success;
    private String message;
    private String username;
    private String fullName;
    private String token;

    public LoginResponse() {}

    public LoginResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public LoginResponse(boolean success, String message, String username, String fullName) {
        this.success = success;
        this.message = message;
        this.username = username;
        this.fullName = fullName;
    }

    public LoginResponse(boolean success, String message, String username, String fullName, String token) {
        this.success = success;
        this.message = message;
        this.username = username;
        this.fullName = fullName;
        this.token = token;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getFullName() {
        return fullName;
    }
    
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
