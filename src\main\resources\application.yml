server:
  port: ${SERVER_PORT:8080}
  shutdown: graceful
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: on_param
    include-exception: false

spring:
  application:
    name: vma-api
  config:
    import: classpath:excel-import.properties

  # R2DBC Configuration for MSSQL
  r2dbc:
    url: ${DB_URL:r2dbc:mssql://localhost:1433/vma_db?preferCursoredExecution=false}
    username: ${DB_USERNAME:mssadmin}
    password: ${DB_PASSWORD:pass}
    pool:
      initial-size: 2
      max-size: 10
      max-idle-time: 10m
      max-acquire-time: 30s
      max-create-connection-time: 30s
      validation-query: SELECT 1
      # Additional pool settings to prevent connection leaks
      enabled: true
      max-life-time: 30m
    # Additional MSSQL-specific properties for better parameter binding
    properties:
      preferCursoredExecution: false
      sendStringParametersAsUnicode: true

  # SQL Initialization disabled - handled by DatabaseMigrationService
  sql:
    init:
      mode: never

  # Jackson Configuration
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      indent-output: true
    deserialization:
      fail-on-unknown-properties: false
      accept-single-value-as-array: true
    time-zone: UTC

  # Security Configuration
  security:
    user:
      name: admin
      password: ${ADMIN_PASSWORD:admin123}
      roles: ADMIN

  # Lifecycle Configuration
  lifecycle:
    timeout-per-shutdown-phase: 30s

# Logging Configuration
logging:
  level:
    root: ${LOG_LEVEL_ROOT:INFO}
    "[com.inspeedia.vanning]": ${LOG_LEVEL_APP:DEBUG}
    "[org.springframework.r2dbc]": ${LOG_LEVEL_DB_QUERY:INFO} # For SQL queries
    "[io.r2dbc]": ${LOG_LEVEL_DB_CON:INFO} # For connection pool issues
    "[io.r2dbc.mssql]": ${LOG_LEVEL_DB_MSSQL:INFO} # For driver/network protocol issues
    "[org.springframework.security]": WARN
    "[org.hibernate]": WARN # For Hibernate SQL queries
    "[org.springframework.transaction]": INFO # For transaction management
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"
  file:
    name: logs/vma-api.log
    max-size: 10MB
    max-history: 30
    total-size-cap: 1GB

# Management and Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,loggers
      base-path: /actuator
  endpoint:
    health:
      show-details: when_authorized
      show-components: always
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
      slo:
        http.server.requests: 10ms, 50ms, 100ms, 200ms, 500ms

# Application Specific Configuration
app:
  cors:
    allowed-origins: ${ALLOWED_ORIGINS:http://localhost:5173}
    allowed-methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed-headers:
      - "*"
    allow-credentials: true
    max-age: 3600

  api:
    version: v1
    base-path: /api/${app.api.version}

  pagination:
    default-page-size: 20
    max-page-size: 100

  database:
    backup:
      enabled: true
      path: ${DB_BACKUP_PATH:./backups}
      retention-days: 30
    migration:
      enabled: true
      rollback-on-error: true
      backup-before-migration: true

# Alert Configuration
alerts:
  enabled:
    "[LATE_START]": true
    "[LOW_PROGRESS]": true
    "[OVER_TIME]": true
  thresholds:
    late-start-minutes: 30
    low-progress-minutes: 40
  rate-thresholds:
    low-progress: 40.0

# OpenAPI Documentation
springdoc:
  api-docs:
    enabled: ${SPRINGDOC_API_DOCS_ENABLED:false}
    path: /api-docs
  swagger-ui:
    enabled: ${SPRINGDOC_SWAGGER_UI_ENABLED:false}
    path: /swagger-ui.html
    operations-sorter: method
    tags-sorter: alpha
  show-actuator: true
