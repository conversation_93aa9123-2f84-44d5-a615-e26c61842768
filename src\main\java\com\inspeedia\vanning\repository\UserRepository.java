package com.inspeedia.vanning.repository;

import com.inspeedia.vanning.domain.User;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * Repository interface for User entity
 *
 * This repository provides reactive database operations for User entities
 * using R2DBC with MSSQL database.
 */
@Repository
public interface UserRepository extends R2dbcRepository<User, Long> {

    /**
     * Find user by username
     */
    Mono<User> findByUsernameAndDeletedFalse(String username);

    /**
     * Find user by username and enabled status
     */
    Mono<User> findByUsernameAndEnabledTrueAndDeletedFalse(String username);

    /**
     * Check if username exists
     */
    Mono<Boolean> existsByUsernameAndDeletedFalse(String username);
}
