package com.inspeedia.vanning.service;

import com.inspeedia.vanning.config.ExcelImportConfig;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.dto.RowDto;
import com.inspeedia.vanning.repository.PlannedWorkRepository;
import com.inspeedia.vanning.service.excel.ExcelStreamingReader;
import jakarta.validation.Validator;

import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.codec.multipart.FilePart;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ExcelImportService
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ExcelImportServiceTest {

    @Mock
    private PlannedWorkRepository plannedWorkRepository;

    @Mock
    private Validator validator;

    @Mock
    private ExcelImportConfig config;

    @Mock
    private FilePart filePart;

    @Mock
    ExcelStreamingReader excelStreamingReader;

    private ExcelImportService excelImportService;

    @BeforeEach
    void setUp() {
        // Mock the config with default values
        ExcelImportConfig.DefaultValues defaultValues = new ExcelImportConfig.DefaultValues();
        defaultValues.setDeliveryPlatform("A");
        defaultValues.setCollectionPlatform("B");
        defaultValues.setSize("L1");

        ExcelImportConfig.ValidationSettings validationSettings = new ExcelImportConfig.ValidationSettings();
        validationSettings.setVanGpPattern("^[A-Z0-9]{2}$");
        validationSettings.setInvalidVanGpValues(new String[]{"0"});

        ExcelImportConfig.EncodingSettings encodingSettings = new ExcelImportConfig.EncodingSettings();
        encodingSettings.setDefaultCharset("UTF-8");
        encodingSettings.setSupportJapanese(true);

        ExcelImportConfig.ProcessingSettings processingSettings = new ExcelImportConfig.ProcessingSettings();
        processingSettings.setSkipHeaderRows(2);
        processingSettings.setBatchSize(100);
        processingSettings.setConcurrencyLevel(16);

        ExcelImportConfig.AuditSettings auditSettings = new ExcelImportConfig.AuditSettings();
        auditSettings.setCreatedBy("excel-import");
        auditSettings.setUpdatedBy("excel-import");

        ExcelImportConfig.FormatSettings formatSettings = new ExcelImportConfig.FormatSettings();
        formatSettings.setDatePatterns(new String[]{"yyyy-MM-dd", "dd/MM/yyyy", "MM/dd/yyyy", "dd-MM-yyyy"});
        formatSettings.setTimePatterns(new String[]{"HH:mm:ss", "HH:mm", "H:mm", "H:mm:ss"});

        ExcelImportConfig.ErrorMessages errorMessages = getExcelImportErrorMessages();

        when(config.getHeaderRowIndex()).thenReturn(1);
        when(config.getDataStartRowIndex()).thenReturn(2);
        when(config.getDefaultValues()).thenReturn(defaultValues);
        when(config.getValidation()).thenReturn(validationSettings);
        when(config.getEncoding()).thenReturn(encodingSettings);
        when(config.getProcessing()).thenReturn(processingSettings);
        when(config.getAudit()).thenReturn(auditSettings);
        when(config.getFormats()).thenReturn(formatSettings);
        when(config.getErrors()).thenReturn(errorMessages);

        // Mock file part
        when(filePart.filename()).thenReturn("計画_20250919.xlsx");

        excelImportService = new ExcelImportService(plannedWorkRepository, validator, config, excelStreamingReader);
    }

    private static ExcelImportConfig.@NotNull ErrorMessages getExcelImportErrorMessages() {
        ExcelImportConfig.ErrorMessages errorMessages = new ExcelImportConfig.ErrorMessages();
        errorMessages.setVanGpRequired("Van GP is required");
        errorMessages.setVanGpPatternMismatch("Van GP must match pattern: {0}");
        errorMessages.setDateRequired("{0} is required");
        errorMessages.setDateInvalid("Invalid {0} format: {1}");
        errorMessages.setTimeRequired("{0} is required");
        errorMessages.setTimeInvalid("Invalid {0} format: {1}");
        errorMessages.setConstraintViolation("Constraint violation while saving record: {0}");
        errorMessages.setTransientDbError("Transient database error (retryable): {0}");
        errorMessages.setTimeoutError("Database operation timed out: {0}");
        errorMessages.setGeneralDbError("Database error: {0}");
        errorMessages.setUnknownDbError("Unknown database error");
        return errorMessages;
    }

    private byte[] loadJapaneseExcelFile() throws IOException {
        ClassPathResource resource = new ClassPathResource("計画.xlsx");
        try (InputStream inputStream = resource.getInputStream()) {
            return inputStream.readAllBytes();
        }
    }

    @Test
    void testImportPlannedWorkFromExcel_Success() throws IOException {
        // Arrange
        byte[] excelData = loadJapaneseExcelFile();
        DataBuffer dataBuffer = new DefaultDataBufferFactory().wrap(excelData);

        when(filePart.content()).thenReturn(Flux.just(dataBuffer));
        when(validator.validate(any(PlannedWork.class))).thenReturn(Collections.emptySet());
        when(plannedWorkRepository.findByWorkDateAndOperatorNameAndVanGp(
                any(LocalDate.class), anyString(), anyString()))
                .thenReturn(Flux.empty());
        when(plannedWorkRepository.save(any(PlannedWork.class)))
                .thenAnswer(invocation -> Mono.just(invocation.getArgument(0)));

        // Mock the ExcelStreamingReader to return empty flux (no rows to process)
        when(excelStreamingReader.read(any())).thenReturn(Flux.empty());

        // Act & Assert
        StepVerifier.create(excelImportService.importPlannedWorkFromExcel(filePart))
                .assertNext(result -> {
                    assertNotNull(result);
                    assertTrue(result.getTotalRecords() >= 0);
                })
                .verifyComplete();
    }

    @Test
    void testImportPlannedWorkFromExcel_InvalidFile() {
        // Arrange
        when(filePart.content()).thenReturn(Flux.error(new RuntimeException("Invalid file")));

        // Act & Assert
        StepVerifier.create(excelImportService.importPlannedWorkFromExcel(filePart))
                .expectError(RuntimeException.class)
                .verify();
    }

    @Test
    void testSkipHeaderRowsPerSheet_MultipleSheets() {
        // Arrange
        when(filePart.filename()).thenReturn("計画_20240101.xlsx");

        // Create mock rows from multiple sheets with headers
        // Expected columns: ROW_NAME(0), DATE(1), DATE_TYPE(2), VAN_GP(3), LOAD_TIME(4), START_TIME(5), END_TIME(6), WORK_TIME(7), VS(8)
        List<RowDto> mockRows = List.of(
            // Sheet 1 - Headers (should be skipped)
            new RowDto(1, "Sheet1", 0, List.of("No", "date", "出荷日", "VANGP", "搬入時間", "開始時間", "終了時間", "作業時間", "VS")),
            new RowDto(2, "Sheet1", 0, List.of("SubHeader1", "SubHeader2", "SubHeader3", "SubHeader4", "SubHeader5", "SubHeader6", "SubHeader7", "SubHeader8", "SubHeader9")),
            // Sheet 1 - Data (should be processed)
            new RowDto(3, "Sheet1", 0, List.of("A1", "2024-01-01", "2024-01-01", "A1", "10:00", "11:00", "12:00", "01:00", "00:30")),
            new RowDto(4, "Sheet1", 0, List.of("A2", "2024-01-01", "2024-01-01", "A2", "13:00", "14:00", "15:00", "01:00", "00:30")),

            // Sheet 2 - Headers (should be skipped)
            new RowDto(1, "Sheet2", 1, List.of("No", "date", "出荷日", "VANGP", "搬入時間", "開始時間", "終了時間", "作業時間", "VS")),
            new RowDto(2, "Sheet2", 1, List.of("SubHeader1", "SubHeader2", "SubHeader3", "SubHeader4", "SubHeader5", "SubHeader6", "SubHeader7", "SubHeader8", "SubHeader9")),
            // Sheet 2 - Data (should be processed)
            new RowDto(3, "Sheet2", 1, List.of("B1", "2024-01-01", "2024-01-01", "B1", "16:00", "17:00", "18:00", "01:00", "00:30")),
            new RowDto(4, "Sheet2", 1, List.of("B2", "2024-01-01", "2024-01-01", "B2", "19:00", "20:00", "21:00", "01:00", "00:30"))
        );

        when(excelStreamingReader.read(any())).thenReturn(Flux.fromIterable(mockRows));
        when(validator.validate(any(PlannedWork.class))).thenReturn(Collections.emptySet());
        when(plannedWorkRepository.findFirstByWorkDateAndRowNameAndOperatorNameAndDeletedFalse(
                any(LocalDate.class), anyString(), anyString()))
                .thenReturn(Mono.empty());
        when(plannedWorkRepository.save(any(PlannedWork.class)))
                .thenAnswer(invocation -> Mono.just(invocation.getArgument(0)));

        DataBuffer dataBuffer = new DefaultDataBufferFactory().wrap(new byte[0]);
        when(filePart.content()).thenReturn(Flux.just(dataBuffer));

        // Act & Assert
        StepVerifier.create(excelImportService.importPlannedWorkFromExcel(filePart))
                .assertNext(result -> {
                    assertNotNull(result);
                    // Should process 4 data rows (2 from each sheet), skipping 4 header rows (2 from each sheet)
                    assertEquals(4, result.getTotalRecords());
                    assertEquals(4, result.getSuccessfulInserts());
                    assertEquals(0, result.getFailedRecords());
                    assertTrue(result.getErrors().isEmpty());
                })
                .verifyComplete();
    }

}
