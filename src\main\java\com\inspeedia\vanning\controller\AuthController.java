package com.inspeedia.vanning.controller;

import com.inspeedia.vanning.dto.LoginRequest;
import com.inspeedia.vanning.dto.LoginResponse;
import com.inspeedia.vanning.dto.PasswordResetRequest;
import com.inspeedia.vanning.dto.SignupRequest;
import com.inspeedia.vanning.security.JwtUtil;
import com.inspeedia.vanning.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * REST controller for authentication operations
 *
 * This controller handles user authentication including login and logout operations.
 */
@RestController
@RequestMapping("/api/v1/auth")
@Tag(name = "Authentication", description = "Authentication operations")
public class AuthController {

    private final Logger log = LoggerFactory.getLogger(AuthController.class);
    private final UserService userService;
    private final JwtUtil jwtUtil;

    public AuthController(UserService userService, JwtUtil jwtUtil) {
        this.userService = userService;
        this.jwtUtil = jwtUtil;
    }

    @Operation(
            summary = "User login",
            description = "Authenticates a user with username and password"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Login successful"),
        @ApiResponse(responseCode = "401", description = "Invalid credentials"),
        @ApiResponse(responseCode = "400", description = "Invalid request data")
    })
    @PostMapping("/login")
    public Mono<ResponseEntity<LoginResponse>> login(@Valid @RequestBody LoginRequest loginRequest) {
        log.info("Login attempt for user: {}", loginRequest.getUsername());

        return userService.authenticate(loginRequest.getUsername(), loginRequest.getPassword())
                .map(user -> {
                    log.info("Login successful for user: {}", user.getUsername());

                    // Generate JWT token
                    String token = jwtUtil.generateToken(user.getUsername(), user.getFullName(), user.getId());

                    LoginResponse response = new LoginResponse(
                            true,
                            "Login successful",
                            user.getUsername(),
                            user.getFullName(),
                            token
                    );
                    return ResponseEntity.ok(response);
                })
                .switchIfEmpty(Mono.fromCallable(() -> {
                    log.warn("Login failed for user: {}", loginRequest.getUsername());
                    LoginResponse response = new LoginResponse(false, "Invalid username or password");
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
                }))
                .onErrorResume(error -> {
                    log.error("Login error for user {}: {}", loginRequest.getUsername(), error.getMessage());
                    LoginResponse response = new LoginResponse(false, "Authentication error");
                    return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response));
                });
    }

    @Operation(
            summary = "User signup",
            description = "Creates a new user account"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "User created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data or username already exists"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/signup")
    public Mono<ResponseEntity<LoginResponse>> signup(@Valid @RequestBody SignupRequest signupRequest) {
        log.info("Signup attempt for user: {}", signupRequest.getUsername());

        return userService.createUser(signupRequest.getUsername(), signupRequest.getPassword(), signupRequest.getFullName())
                .map(user -> {
                    log.info("Signup successful for user: {}", user.getUsername());
                    LoginResponse response = new LoginResponse(
                            true,
                            "User created successfully",
                            user.getUsername(),
                            user.getFullName()
                    );
                    return ResponseEntity.status(HttpStatus.CREATED).body(response);
                })
                .onErrorResume(IllegalArgumentException.class, error -> {
                    log.warn("Signup failed for user {}: {}", signupRequest.getUsername(), error.getMessage());
                    LoginResponse response = new LoginResponse(false, error.getMessage());
                    return Mono.just(ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response));
                })
                .onErrorResume(error -> {
                    log.error("Signup error for user {}: {}", signupRequest.getUsername(), error.getMessage());
                    LoginResponse response = new LoginResponse(false, "User creation failed");
                    return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response));
                });
    }

    @Operation(
            summary = "User logout",
            description = "Logs out the current user"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Logout successful")
    })
    @PostMapping("/logout")
    public Mono<ResponseEntity<LoginResponse>> logout() {
        log.info("User logout");

        LoginResponse response = new LoginResponse(true, "Logout successful");
        return Mono.just(ResponseEntity.ok(response));
    }

    @Operation(
            summary = "Check authentication status",
            description = "Checks if the current session is authenticated by validating the JWT token"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Authentication status retrieved"),
        @ApiResponse(responseCode = "401", description = "Not authenticated or invalid token")
    })
    @GetMapping("/status")
    public Mono<ResponseEntity<LoginResponse>> status(ServerHttpRequest request) {
        log.debug("Authentication status check");

        // Extract JWT token from Authorization header
        String authHeader = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);

        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            log.debug("No valid Authorization header found");
            LoginResponse response = new LoginResponse(false, "Not authenticated");
            return Mono.just(ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response));
        }

        String token = authHeader.substring(7); // Remove "Bearer " prefix

        try {
            // Validate token
            if (!jwtUtil.validateToken(token)) {
                log.debug("Invalid or expired JWT token");
                LoginResponse response = new LoginResponse(false, "Invalid or expired token");
                return Mono.just(ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response));
            }

            // Extract user information from token
            String username = jwtUtil.extractUsername(token);
            String fullName = jwtUtil.extractFullName(token);
            Long userId = jwtUtil.extractUserId(token);

            log.debug("Authentication status check successful for user: {}", username);

            // Return authenticated status with user information
            LoginResponse response = new LoginResponse(
                    true,
                    "Authenticated",
                    username,
                    fullName,
                    token
            );
            return Mono.just(ResponseEntity.ok(response));

        } catch (Exception e) {
            log.error("Error validating token: {}", e.getMessage());
            LoginResponse response = new LoginResponse(false, "Token validation failed");
            return Mono.just(ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response));
        }
    }

    @Operation(
            summary = "Reset user password",
            description = "Resets the password for a user account"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Password reset successful"),
        @ApiResponse(responseCode = "400", description = "Invalid request data or user not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/reset-password")
    public Mono<ResponseEntity<LoginResponse>> resetPassword(@Valid @RequestBody PasswordResetRequest resetRequest) {
        log.info("Password reset attempt for user: {}", resetRequest.getUsername());

        return userService.updatePassword(resetRequest.getUsername(), resetRequest.getNewPassword())
                .map(user -> {
                    log.info("Password reset successful for user: {}", user.getUsername());
                    LoginResponse response = new LoginResponse(
                            true,
                            "Password reset successfully",
                            user.getUsername(),
                            user.getFullName()
                    );
                    return ResponseEntity.ok(response);
                })
                .onErrorResume(IllegalArgumentException.class, error -> {
                    log.warn("Password reset failed for user {}: {}", resetRequest.getUsername(), error.getMessage());
                    LoginResponse response = new LoginResponse(false, error.getMessage());
                    return Mono.just(ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response));
                })
                .onErrorResume(error -> {
                    log.error("Password reset error for user {}: {}", resetRequest.getUsername(), error.getMessage());
                    LoginResponse response = new LoginResponse(false, "Password reset failed");
                    return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response));
                });
    }
}
