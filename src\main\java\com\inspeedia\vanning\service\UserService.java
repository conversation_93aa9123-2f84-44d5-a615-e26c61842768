package com.inspeedia.vanning.service;

import com.inspeedia.vanning.domain.User;
import com.inspeedia.vanning.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * Service for managing user operations
 *
 * This service handles user authentication, creation, and management operations.
 */
@Service
@Transactional
public class UserService {

    private final Logger log = LoggerFactory.getLogger(UserService.class);
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    public UserService(UserRepository userRepository, PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }

    /**
     * Authenticate user with username and password
     */
    public Mono<User> authenticate(String username, String password) {
        log.debug("Authenticating user: {}", username);
        
        return userRepository.findByUsernameAndEnabledTrueAndDeletedFalse(username)
                .filter(user -> passwordEncoder.matches(password, user.getPassword()))
                .doOnSuccess(user -> {
                    if (user != null) {
                        log.debug("Authentication successful for user: {}", username);
                    } else {
                        log.debug("Authentication failed for user: {}", username);
                    }
                })
                .doOnError(error -> log.error("Authentication error for user {}: {}", username, error.getMessage()));
    }

    /**
     * Find user by username
     */
    public Mono<User> findByUsername(String username) {
        log.debug("Finding user by username: {}", username);
        
        return userRepository.findByUsernameAndDeletedFalse(username)
                .doOnSuccess(user -> log.debug("User found: {}", user != null ? user.getUsername() : "null"))
                .doOnError(error -> log.error("Error finding user {}: {}", username, error.getMessage()));
    }

    /**
     * Create a new user
     */
    public Mono<User> createUser(String username, String password, String fullName) {
        log.debug("Creating new user: {}", username);
        
        return userRepository.existsByUsernameAndDeletedFalse(username)
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.error(new IllegalArgumentException("Username already exists: " + username));
                    }
                    
                    User user = new User();
                    user.setUsername(username);
                    user.setPassword(passwordEncoder.encode(password));
                    user.setFullName(fullName);
                    user.setEnabled(true);
                    
                    LocalDateTime now = LocalDateTime.now();
                    user.setCreatedAt(now);
                    user.setUpdatedAt(now);
                    user.setCreatedBy("system");
                    user.setUpdatedBy("system");
                    
                    return userRepository.save(user);
                })
                .doOnSuccess(user -> log.debug("User created successfully: {}", user.getUsername()))
                .doOnError(error -> log.error("Error creating user {}: {}", username, error.getMessage()));
    }

    /**
     * Update user password
     */
    public Mono<User> updatePassword(String username, String newPassword) {
        log.debug("Updating password for user: {}", username);
        
        return userRepository.findByUsernameAndDeletedFalse(username)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("User not found: " + username)))
                .flatMap(user -> {
                    user.setPassword(passwordEncoder.encode(newPassword));
                    user.setUpdatedAt(LocalDateTime.now());
                    user.setUpdatedBy("system");
                    
                    return userRepository.save(user);
                })
                .doOnSuccess(user -> log.debug("Password updated successfully for user: {}", user.getUsername()))
                .doOnError(error -> log.error("Error updating password for user {}: {}", username, error.getMessage()));
    }

    /**
     * Enable or disable user
     */
    public Mono<User> setUserEnabled(String username, boolean enabled) {
        log.debug("Setting user {} enabled status to: {}", username, enabled);
        
        return userRepository.findByUsernameAndDeletedFalse(username)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("User not found: " + username)))
                .flatMap(user -> {
                    user.setEnabled(enabled);
                    user.setUpdatedAt(LocalDateTime.now());
                    user.setUpdatedBy("system");
                    
                    return userRepository.save(user);
                })
                .doOnSuccess(user -> log.debug("User {} enabled status updated to: {}", user.getUsername(), enabled))
                .doOnError(error -> log.error("Error updating enabled status for user {}: {}", username, error.getMessage()));
    }
}
