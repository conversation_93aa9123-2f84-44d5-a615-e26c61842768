package com.inspeedia.vanning.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

/**
 * Configuration class for Netty settings
 * 
 * This configuration handles Netty ByteBuf leak detection and memory management
 * settings to prevent memory leaks in reactive applications using R2DBC.
 */
@Configuration
public class NettyConfig {

    private static final Logger log = LoggerFactory.getLogger(NettyConfig.class);

    /**
     * Configures Netty system properties to handle ByteBuf leaks
     * 
     * This method sets system properties that control Netty's memory management
     * and leak detection behavior to prevent ByteBuf memory leaks.
     */
    @PostConstruct
    public void configureNetty() {
        log.info("Configuring Netty settings for ByteBuf leak prevention");
        
        // Disable leak detection in production to avoid performance overhead
        System.setProperty("io.netty.leakDetection.level", "DISABLED");
        
        // Use pooled allocator for better performance
        System.setProperty("io.netty.allocator.type", "pooled");
        
        // Disable direct memory allocation to reduce leak potential
        System.setProperty("io.netty.noPreferDirect", "true");
        
        // Set number of direct arenas to 0 to avoid direct memory issues
        System.setProperty("io.netty.allocator.numDirectArenas", "0");
        
        // Configure buffer leak detection target
        System.setProperty("io.netty.leakDetection.targetRecords", "4");
        
        log.info("Netty configuration completed - ByteBuf leak detection: DISABLED");
    }
}
